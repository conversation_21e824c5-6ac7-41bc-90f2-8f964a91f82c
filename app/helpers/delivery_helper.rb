module DeliveryHelper
  include Rails.application.routes.url_helpers

  def self.build_delivery_index(delivery, current_user)
    order = delivery.order_transactions.first
    location_to,
    fulfillment_location = DeliveryHelper.build_location_to_and_fulfillment_location(delivery, order)
    location_from = LocationHelper.build_order_location_detail_location_to(order, current_user)

    {
      location_from: location_from,
      location_to: location_to,
      fulfillment_location: fulfillment_location,
      id: delivery.id,
      delivery_no: delivery.delivery_no,
      status: delivery.status,
      delivery_date: delivery.delivery_date.strftime('%d/%m/%Y'),
      has_packing: delivery.delivery_packings.present?
    }
  end

  def self.build_location_to_and_fulfillment_location(delivery, order)
    if delivery.delivery_fulfillment?
      location_to = LocationHelper.build_order_location_detail_fulfillment_location(order)
      fulfillment_location = LocationHelper.build_order_location_detail_location_from(order)
      return [location_to, fulfillment_location]
    end

    location_to = LocationHelper.build_order_location_detail_location_from(order)
    [location_to, nil]
  end

  # rubocop:disable Metrics/AbcSize, Metrics/ParameterLists, Metrics/MethodLength
  def self.build_delivery_detail(delivery, with_acceptance_information = false, pdf_format = false, show_summary_lines, current_user,
                                 differ_logo_brand: false)
    delivery_detail = delivery.presentation_attributes
    delivery_detail[:status] = delivery.status
    delivery_detail[:status_name] = delivery.status_name
    delivery_detail[:received_date] = delivery&.received_date&.strftime('%d/%m/%Y')
    delivery_detail[:delivery_date] = delivery.delivery_date.strftime('%d/%m/%Y')
    delivery_detail[:has_packing] = delivery.delivery_packings.present?
    delivery_detail[:vendor_notes] = delivery.vendor_notes

    order = delivery.order_transactions.first
    delivery_detail = build_delivery_detail_locations(delivery, delivery_detail, order, current_user, pdf_format)

    if pdf_format
      delivery_detail[:is_multibrand] = delivery.is_multibrand
      delivery_detail[:multibrand_logo_display] = delivery.multibrand_logo_display(differ_logo_brand: differ_logo_brand)
      delivery_detail[:multibrand_brand_name_display] = delivery.multibrand_brand_name_display(differ_logo_brand: differ_logo_brand)
    end

    if show_summary_lines
      delivery_detail[:subtotal_amount] = delivery.delivery_transaction_lines.reduce(0) { |sum, line| sum + line.final_subtotal }
      delivery_detail[:tax_amount] = delivery.delivery_transaction_lines.reduce(0) { |sum, line| sum + line.final_tax_amount }
      delivery_detail[:discount_amount] = delivery.delivery_transaction_lines.reduce(0) do |sum, line|
        sum + line.total_discount_per_product_by_received_quantity
      end
      delivery_detail[:total_amount] = delivery_detail[:subtotal_amount] + delivery_detail[:tax_amount] - delivery_detail[:discount_amount]
    end

    delivery_detail[:delivery_proofs] = delivery.delivery_proofs.map do |proof|
      {
        name: proof['name'],
        url: FileHelper.presigned_url(proof['url']),
        from_camera: proof['from_camera'].presence
      }
    end

    delivery_detail[:delivery_returns] = delivery.generate_delivery_returns

    build_delivered_info_detail(delivery_detail, delivery) if with_acceptance_information

    delivery_detail.with_indifferent_access
  end
  # rubocop:enable Metrics/AbcSize, Metrics/ParameterLists, Metrics/MethodLength

  def self.build_delivery_detail_locations(delivery, delivery_detail, order, current_user, pdf_format)
    delivery_detail[:location_from] = LocationHelper.build_order_location_detail_location_to(order, current_user)
    delivery_detail[:location_to],
    delivery_detail[:fulfillment_location] = DeliveryHelper.build_location_to_and_fulfillment_location(delivery, order)
    if delivery.delivery_fulfillment?
      mask_order_fulfillment = DeliveryHelper.obtain_mask_third_party_location_status_for_delivery_fulfillment(delivery, pdf_format, current_user)
      delivery_detail[:mask_third_party_location_and_preview_delivery_fulfillment] =
        delivery.mask_third_party_location_and_preview_delivery_fulfillment?(current_user)
      delivery_detail[:mask_third_party_location_and_preview_order_fulfillment] = mask_order_fulfillment
    else
      delivery_detail = delivery_detail.except('fulfillment_location_id')
    end

    delivery_detail[:location_from] =
      LocationHelper.build_order_location_detail_location_to(order, current_user)
    delivery_detail[:location_to],
    delivery_detail[:fulfillment_location] = DeliveryHelper.build_location_to_and_fulfillment_location(delivery, order)
    delivery_detail
  end

  def self.obtain_mask_third_party_location_status_for_delivery_fulfillment(delivery, pdf_format, current_user)
    if pdf_format
      delivery.mask_third_party_location_for_pdf?(current_user)
    else
      delivery.mask_third_party_location_and_preview_order_fulfillment?(current_user)
    end
  end

  def self.build_delivered_info_detail(delivery_detail, delivery)
    pic = delivery.pic
    delivery_accepted_date = delivery.delivery_acceptance_notes.try(:first).try(:created_at)
    delivery_detail[:delivery_accepted_date] = delivery_accepted_date.strftime('%d/%m/%Y') if delivery_accepted_date.present?

    delivery_detail[:delivery_acceptance_notes] = delivery.delivery_acceptance_notes.map do |accept_note|
      { note_type: accept_note.note_type, message: accept_note.message }
    end

    delivery_detail[:pic] =
      ({ id: delivery.pic_id, fullname: delivery.pic_fullname, avatar_url: FileHelper.presigned_url(pic.avatar_url) } if pic.present?)

    delivery_detail[:acceptance_proofs] = delivery.acceptance_proofs.map do |proof|
      {
        name: proof['name'],
        url: FileHelper.presigned_url(proof['url'])
      }
    end
  end

  def self.build_category_delivery(delivery_lines, show_price = false, group_by_category_summary)
    categories_delivery_lines = delivery_lines.includes([order_transaction_line: [{ product: :product_category }]])
                                              .group_by { |delivery_line| delivery_line.order_transaction_line.product.product_category }

    categories_delivery_lines.map do |category, grouped_delivery_lines|
      lines = if group_by_category_summary
                build_product_with_delivery_lines(grouped_delivery_lines, show_price)
              else
                build_product_with_delivery_lines_per_order(grouped_delivery_lines, show_price)
              end

      {
        category: category,
        delivery_lines: lines
      }
    end
  end

  # rubocop:disable Style/MultilineBlockChain
  def self.build_packing_delivery_lines(delivery, delivery_packings)
    data = delivery_packings.map do |packing|
      {
        name: packing.name,
        details: packing.delivery_packing_details.map do |detail|
          product_qty = delivery.order_transaction_lines.where(product_id: detail.product_id).select do |line|
            line.product_unit_id == detail.product_unit_id
          end.map do |line|
            line.product.convert_quantity(line.product_unit_id, detail.product_unit_id, line.product_qty)
          end.sum.to_d

          received_quantity = delivery.delivery_transaction_lines.select do |line|
            line.order_transaction_line.product_id == detail.product_id && line.order_transaction_line.product_unit_id == detail.product_unit_id
          end.map(&:received_quantity).sum.to_d
          {
            product: {
              id: detail.product_id,
              name: detail.product_name,
              sku: detail.product_sku
            },
            product_unit: { id: detail.product_unit_id, name: detail.product_unit_name },
            delivered_qty: detail.quantity,
            received_quantity: received_quantity,
            product_qty: product_qty
          }
        end
      }
    end
    {
      delivery_lines: data
    }
  end
  # rubocop:enable Style/MultilineBlockChain

  def self.build_order_delivery_lines(delivery_lines, show_price = false)
    grouped_deliveries_by_order = delivery_lines.includes(order_transaction_line: [
                                                            :delivery_transaction_lines, {
                                                              order_transaction_line_fulfillments: :delivery_transaction_lines
                                                            }
                                                          ]).group_by(&:order_transaction_id)

    grouped_deliveries_by_order.keys.map do |order_id|
      delivery_lines = grouped_deliveries_by_order[order_id]
      order_detail = delivery_lines.first.order_transaction

      {
        order_detail: order_detail.attributes.except('custom_numbering_pattern')
                                  .with_indifferent_access
                                  .merge(vendor_notes: order_detail.vendor_notes),
        delivery_lines: delivery_lines.map { |delivery| build_delivery_detail_line(delivery, show_price) }
      }
    end
  end

  def self.inject_can_receive_flag(delivery_detail, delivery, current_user)
    delivery_detail[:can_receive] = if delivery.location_to_type.eql?('Location')
                                      delivery.sent? &&
                                        current_user.available_locations.where(id: delivery.location_to_id).present? &&
                                        Api::DeliveriesPolicy.new(current_user, delivery).receive?
                                    else
                                      delivery.sent? && Api::DeliveriesPolicy.new(current_user, delivery).receive?
                                    end
    delivery_detail.with_indifferent_access
  end

  def self.inject_can_return_flag(delivery_detail, delivery)
    can_return = false
    delivery.delivery_transaction_lines.each do |line|
      if (line.received_quantity.to_d - line.metadata['adjusted_qty'].to_d).positive?
        can_return = true
        break
      end
    end

    delivery_detail[:can_return] = can_return
  end

  def self.inject_can_edit_flag(delivery_detail, delivery, current_user)
    delivery_detail[:can_edit] = if delivery.location_from_type.eql?('Location') && delivery.location_to_type.eql?('Location')
                                   current_user.available_locations.where(id: delivery.location_from_id).present? &&
                                     Api::DeliveriesPolicy.new(current_user, delivery).update?
                                 else
                                   false
                                 end
    delivery_detail.with_indifferent_access
  end

  # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
  def self.build_delivery_detail_line(delivery_line, show_price = false)
    detail_obj = delivery_line.attributes.except('costing_ck_status')
    order_detail = delivery_line.order_transaction_line

    estimated_costs = if delivery_line.delivery_transaction.internal?
                        Restaurant::Services::Product::BulkEstimateCostOneLocationService
                          .new(
                            date: Time.now.in_time_zone(delivery_line.location_from.timezone).to_date,
                            location: delivery_line.location_from,
                            products: [order_detail.product]
                          ).call
                      else
                        'N/A'
                      end

    detail_obj[:product] = {
      name: order_detail.product_name,
      id: order_detail.product_id,
      sku: order_detail.product_sku,
      description: order_detail.product_description,
      image_url: order_detail.product.image_url.presence || ''
    }

    detail_obj[:product_unit] = {
      name: order_detail.product_unit_name
    }

    inventory_out = if delivery_line.delivery_transaction.location_from.instance_of?(Location)
                      Inventory.find_by(resource: delivery_line.delivery_transaction,
                                        resource_line: delivery_line,
                                        location_id: delivery_line.delivery_transaction.location_from_id)
                    end
    detail_obj[:expiry_details_out] = inventory_out&.expiry_data
    detail_obj[:product_qty] = order_detail.product_qty.to_d
    detail_obj[:open_qty] = order_detail.open_qty.to_d.positive? ? order_detail.open_qty.to_d : 0.to_d
    detail_obj[:delivered_qty] = delivery_line.delivered_qty.to_d
    detail_obj[:allow_max_receive_qty] = delivery_line.max_received_qty.to_d
    detail_obj[:adjusted_qty] = delivery_line.metadata['adjusted_qty'].to_d # made by delivery return
    detail_obj[:received_quantity] = delivery_line.received_quantity.to_d
    detail_obj[:estimated_costs] = estimated_costs[order_detail.product_id]
    if show_price
      detail_obj[:price] = delivery_line.order_transaction_line.product_buy_price.to_d
      detail_obj[:discount] = delivery_line.total_discount_per_product_by_received_quantity
      detail_obj[:total] = delivery_line.total_buy_price_after_discount_total_by_received_quantity
    end

    detail_obj.with_indifferent_access
  end

  # rubocop:disable Metrics/BlockLength
  def self.build_product_with_delivery_lines(delivery_lines, show_price = false)
    data = []
    product_delivery_lines = delivery_lines.group_by do |x|
      [
        x.order_transaction_line.product_id,
        x.order_transaction_line.product_unit_id,
        x.order_transaction_line.product_unit_conversion_id
      ]
    end

    product_delivery_lines.each do |ids, grouped_delivery_lines|
      product_id = ids[0]
      product_unit_id = ids[1]
      product_unit_conversion_id = ids[2]

      total_delivered_qty = grouped_delivery_lines.map(&:delivered_qty).sum
      total_received_quantity = grouped_delivery_lines.map(&:received_quantity).sum
      first_order_line = grouped_delivery_lines.first.order_transaction_line
      total_adjusted_qty = grouped_delivery_lines.inject(0) { |total, line| total + line.metadata['adjusted_qty'].to_d }

      order_transaction_lines = OrderTransactionLine.where(
        id: grouped_delivery_lines.map(&:order_transaction_line_id).uniq,
        product_id: product_id,
        product_unit_id: product_unit_id,
        product_unit_conversion_id: product_unit_conversion_id
      )

      line_response = {
        product_qty: order_transaction_lines.sum(&:product_qty).to_d,
        delivered_qty: total_delivered_qty.to_d,
        received_quantity: total_received_quantity.to_d,
        adjusted_qty: total_adjusted_qty.to_d,
        product_unit: { id: first_order_line.product_unit_id, name: first_order_line.product_unit_name },
        product: {
          id: first_order_line.product_id,
          name: first_order_line.product_name,
          sku: first_order_line.product_sku,
          description: first_order_line.product_description
        }
      }

      if show_price
        line_response[:price] = grouped_delivery_lines.map { |line| line.order_transaction_line.product_buy_price.to_d }.sum
        line_response[:discount] = grouped_delivery_lines.map(&:total_discount_per_product_by_received_quantity).sum
        line_response[:total] = grouped_delivery_lines.map(&:total_buy_price_after_discount_total_by_received_quantity).sum
      end

      data.push(line_response)
    end

    data
  end

  def self.build_product_with_delivery_lines_per_order(delivery_lines, show_price = false)
    data = []

    delivery_lines.each do |delivery_line|
      total_delivered_qty = delivery_line.delivered_qty
      total_received_quantity = delivery_line.received_quantity
      first_order_line = delivery_line.order_transaction_line
      total_adjusted_qty = delivery_line.metadata['adjusted_qty'].to_d

      line_response = {
        id: delivery_line.id,
        product_qty: delivery_line.order_transaction_line.product_qty,
        delivered_qty: total_delivered_qty.to_d,
        received_quantity: total_received_quantity.to_d,
        adjusted_qty: total_adjusted_qty.to_d,
        product_unit: { id: first_order_line.product_unit_id, name: first_order_line.product_unit_name },
        product: {
          id: first_order_line.product_id,
          name: first_order_line.product_name,
          sku: first_order_line.product_sku,
          description: first_order_line.product_description
        }
      }

      if show_price
        line_response[:price] = delivery_line.order_transaction_line.product_buy_price.to_d
        line_response[:discount] = delivery_line.total_discount_per_product_by_received_quantity
        line_response[:total] = delivery_line.total_buy_price_after_discount_total_by_received_quantity
      end

      data.push(line_response)
    end

    data
  end
  # rubocop:enable Metrics/AbcSize, Metrics/BlockLength

  def self.get_status_color_code(status)
    return '#0A54FF' if status == 'sent'

    return '#06846B' if status == 'delivered'

    '#CB2A4A'
  end
  # rubocop:enable Metrics/MethodLength
end
