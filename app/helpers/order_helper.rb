# will be review and rewrite
module OrderHelper
  include Restaurant::Modules::Procurement::Constants

  def self.build_order_index(order, can_see_payment_status, current_user, params = {})
    base_response = {
      location_to: LocationHelper.build_order_location_detail_location_to(order, current_user),
      location_from: LocationHelper.build_order_location_detail_location_from(order),
      fulfillment_location: order.order_fulfillment? ? LocationHelper.build_order_location_detail_fulfillment_location(order) : nil,
      status: order.status,
      order_no: order.order_no,
      order_date: order.order_date.strftime('%d/%m/%Y'),
      request_delivery_date: order.request_delivery_date&.strftime('%d/%m/%Y'),
      location_from_id: order.location_from_id,
      location_to_id: order.location_to_id,
      fulfillment_location_id: order.fulfillment_location_id,
      id: order.id,
      notes: order.notes,
      closed_notes: order.closed_notes,
      is_bulk_order: order.is_bulk_order,
      total_amount: order.total_amount
    }

    base_response[:payment_status] = order.payment_status if can_see_payment_status
    base_response[:show_order_on_seller] = order.not_waiting_for_buyer? if params[:order_nature] == Restaurant::Constants::INCOMING

    base_response
  end

  def self.build_delivery(delivery)
    delivery_detail = delivery.attributes
    delivery_detail[:delivery_date] = delivery.delivery_date.strftime('%d/%m/%Y')
    delivery_detail[:transaction_date] = delivery.delivery_date.strftime('%d/%m/%Y')
    delivery_detail[:transaction_no] = delivery.delivery_no
    delivery_detail[:status_name] = delivery.status_name
    delivery_detail[:type] = 'Delivery'

    delivery_detail.with_indifferent_access
  end

  def self.build_delivery_return(delivery_return)
    delivery_return_detail = delivery_return.attributes
    delivery_return_detail[:transaction_date] = delivery_return.return_date.strftime('%d/%m/%Y')
    delivery_return_detail[:transaction_no] = delivery_return.return_no
    delivery_return_detail[:status_name] = delivery_return.status_name
    delivery_return_detail[:type] = 'Return'

    delivery_return_detail.with_indifferent_access
  end

  def self.build_related_order(order, type = 'Fulfillment')
    order_detail = order.attributes

    order_detail[:transaction_date] = order.order_date.strftime('%d/%m/%Y')
    order_detail[:transaction_no] = order.order_no
    order_detail[:status_name] = order.status_name
    order_detail[:type] = type

    order_detail.with_indifferent_access
  end

  def self.build_order_detail_lines_with_delivery(order:, can_manage_price_and_discount: false)
    order.order_transaction_lines.order_by_id.map do |detail|
      params = {
        order: order,
        order_transaction_line: detail,
        show_delivered_qty: order.closed?,
        can_manage_price_and_discount: can_manage_price_and_discount
      }

      detail_obj = if order.order_fulfillment?
                     Restaurant::Services::Procurement::OrderLineFulfillmentDetailResponseBuilder.new(params: params).call
                   else
                     Restaurant::Services::Procurement::OrderLineDetailResponseBuilder.new(params: params).call
                   end

      detail_obj[:open_qty] = detail.open_qty.to_d.positive? ? detail.open_qty.to_d : 0.to_d
      detail_obj[:allow_max_delivery_qty] = detail.open_qty + detail.buffer_delivery_qty
      detail_obj[:allow_max_receive_qty] = detail.max_receive_qty.to_d
      detail_obj.with_indifferent_access
    end
  end

  def self.get_status_color_code(status)
    return '#595E69' if status == 'pending'

    return '#0A54FF' if status == 'processing'

    return '#06846B' if status == 'closed'

    return '#06846B' if status == 'manually_closed'

    ::ReportHelper::COLOR_RED
  end

  def self.get_text_color_code(status)
    return ::ReportHelper::COLOR_RED if status == 'void'

    '#000000'
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def self.build_order_line_pdf_detail(order_line, can_manage_price_and_discount, is_order_closed)
    response = {
      product_name: order_line.product_name,
      product_unit_name: order_line.product_unit_name,
      product_qty: order_line.product_qty,
      product_buy_price: can_manage_price_and_discount ? order_line.product_buy_price : nil,
      product_sku: order_line.product_sku,
      discount: order_line.discount,
      discount_amount: order_line.discount,
      discount_total: order_line.discount_total,
      subtotal: order_line.subtotal_line_payment_amount,
      total_tax: 0
    }

    if is_order_closed
      delivery_lines = order_line.delivery_transaction_lines
      response[:delivered_qty] = order_line.total_delivered_or_received_quantity_including_fulfillments.to_d
      response[:return_quantity] = order_line.accepted_return_quantity(delivery_lines).to_d + order_line.fulfillment_return_qty.to_d
      final_qty = response[:delivered_qty].to_d - response[:return_quantity].to_d
      final_to_product_qty_ratio = final_qty / order_line.product_qty
      subtotal = (order_line.product_buy_price.to_d * final_qty)

      if can_manage_price_and_discount
        response[:discount_amount] = order_line.discount_exclude_promo_total_order_by_delivered_quantity.to_d
        response[:total_amount_after_returns] = subtotal - (final_to_product_qty_ratio * order_line.discount_total.to_d)
        response[:total_amount_without_global_promo] =
          subtotal - (final_to_product_qty_ratio * order_line.discount_item_exclude_promo_total_order.to_d)
      end
    elsif can_manage_price_and_discount
      response[:total_amount_after_returns] = order_line.total_amount
      response[:total_amount_without_global_promo] = order_line.total_amount_without_global_promo
    end

    if order_line.tax_id.present? && order_line.tax_rate.present?
      response[:total_tax] = order_line.calculate_total_tax_order_line_pdf(response[:total_amount_after_returns])
    end

    response
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  # rubocop:disable Metrics/MethodLength
  def self.build_order_pdf_detail(order, **args)
    show_delivered_qty = order.closed?

    order_detail = Restaurant::Services::Procurement::OrderPdfBaseResponsePresenter.new(order, show_delivered_qty, args[:current_user]).call
    order_transaction_lines = order.order_transaction_lines
                                   .order_by_id.includes(delivery_transaction_lines: :delivery_returns,
                                                         order_transaction_line_fulfillments: [
                                                           { delivery_transaction_lines: :delivery_returns }
                                                         ])
    response_order_lines = []

    if args[:group_by] == GROUP_BY_CATEGORY_GROUP && args[:has_category_group]
      response_order_lines, extra_objects = Restaurant::Services::Procurement::PdfBuildOrderLinesPerCategoryGroup
                                            .new(order_detail, order_transaction_lines,
                                                 can_manage_price_and_discount: args[:can_manage_price_and_discount],
                                                 show_delivered_qty: show_delivered_qty,
                                                 has_category_group: args[:has_category_group]).call!
    elsif args[:is_group_by_category] || args[:group_by] == GROUP_BY_CATEGORY_GROUP
      response_order_lines, extra_objects = Restaurant::Services::Procurement::PdfBuildOrderLinesPerCategory
                                            .new(order_detail, order_transaction_lines,
                                                 can_manage_price_and_discount: args[:can_manage_price_and_discount],
                                                 show_delivered_qty: show_delivered_qty,
                                                 has_category_group: args[:has_category_group]).call!
    else
      response_order_lines = OrderHelper.build_draft_response_order_lines(order_transaction_lines, args[:can_manage_price_and_discount],
                                                                          show_delivered_qty)
      order_detail = OrderHelper.for_pdf_calculate_total_amount_by_order_lines(order_detail, response_order_lines)
    end

    order_detail[:total_amount_after_returns] =
      order_detail[:total_amount_after_returns].to_d + order_detail[:shipping_fee].to_d + order_detail[:total_tax].to_d
    order_detail[:total_amount_after_returns] += if order.instance_of?(OrderTransaction)
                                                   order_detail[:transaction_fee].to_d
                                                 else
                                                   order_detail[:items_transaction_fee].to_d
                                                 end
    order_detail[:closed_by_user] = OrderHelper.incomplete_order_closed_by_user?(order, order_detail)

    {
      order_detail: order_detail,
      order_detail_lines: response_order_lines,
      brand: order.brand,
      extra_objects: extra_objects,
      fulfiller_brand: order_detail[:is_order_fulfillment] ? order.location_to.brand : nil
    }
  end
  # rubocop:enable Metrics/MethodLength

  def self.build_draft_response_order_lines(order_transaction_lines, can_manage_price_and_discount, show_delivered_qty)
    order_transaction_lines.map do |order_line|
      OrderHelper.build_order_line_pdf_detail(order_line, can_manage_price_and_discount, show_delivered_qty)
    end
  end

  def self.for_pdf_calculate_total_amount_by_order_lines(order_detail, response_order_lines)
    response_order_lines.each do |order_line|
      order_detail[:subtotal_amount] += order_line[:subtotal].to_d
      order_detail[:total_amount_without_global_promo] += order_line[:total_amount_without_global_promo].to_d
      order_detail[:total_amount_after_returns] += order_line[:total_amount_after_returns].to_d
      order_detail[:total_tax] += order_line[:total_tax].to_d
      order_detail[:has_return_transaction] = true if order_line[:return_quantity].to_d.positive?
    end

    order_detail
  end

  def self.inject_order_lines_per_product_category(order_detail_lines, category, per_category_order_lines)
    order_detail_lines.push({
                              category: {
                                id: category.try(:id),
                                name: category.try(:name).presence || I18n.t('product_categories.uncategorized'),
                                product_category_group_id: category.try(:product_category_group_id).presence
                              },
                              order_detail_lines: per_category_order_lines
                            })

    order_detail_lines
  end

  def self.closed_by_user(order)
    order.closed? && !order.order_transaction_lines
                           .sum(&:unreceived_qty).zero?
  end

  def self.get_order_status_name(order)
    return I18n.t('report.orders.closed_by_user') if OrderHelper.closed_by_user(order)

    order.status_name
  end

  def self.incomplete_order_closed_by_user?(order, order_detail)
    # order.subtotal_amount will refer to product qty * product_buy_price
    # while order_detail[:subtotal_amount] will refer to delivered qty * product_buy_price
    OrderHelper.closed_by_user(order) && order.subtotal_amount != order_detail[:subtotal_amount]
  end

  def self.get_stock_availability(location_id, product_id)
    LocationsProduct.find_by(location_id: location_id,
                             product_id: product_id)&.available_stock_flag_procurement || false
  end
end
