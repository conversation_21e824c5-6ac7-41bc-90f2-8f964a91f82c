class Restaurant::Models::DineInFeeSetting < ApplicationRecord
  belongs_to :dine_in_fee_setupable, polymorphic: true

  ALLOWED_COUNTRY = ['Indonesia'].freeze

  PAYMENT_METHOD_VA = 'va'.freeze
  PAYMENT_METHOD_CC = 'cc'.freeze
  PAYMENT_METHOD_GOPAY = 'gopay'.freeze
  PAYMENT_METHOD_OVO = 'ovo'.freeze
  PAYMENT_METHOD_DANA = 'dana'.freeze
  PAYMENT_METHOD_LINKAJA = 'linkaja'.freeze
  PAYMENT_METHOD_SHOPEEPAY = 'shopeepay'.freeze
  PAYMENT_METHOD_SAKUKU = 'sakuku'.freeze
  PAYMENT_METHOD_QRIS = 'qris'.freeze
  PAYMENT_METHOD_VIA_CASHIER = 'via_cashier'.freeze
  PAYMENT_METHOD_FULL_BALANCE = 'full_balance'.freeze

  FEE_SELECTOR_MAP = {
    virtual_account: PAYMENT_METHOD_VA,
    credit_card: PAYMENT_METHOD_CC,
    qris: PAYMENT_METHOD_QRIS,
    gopay: PAYMENT_METHOD_GOPAY,
    ovo: PAYMENT_METHOD_OVO,
    dana: PAYMENT_METHOD_DANA,
    linkaja: PAYMENT_METHOD_LINKAJA,
    shopeepay: PAYMENT_METHOD_SHOPEEPAY,
    sakuku: PAYMENT_METHOD_SAKUKU,
    cash: PAYMENT_METHOD_VIA_CASHIER,
    balance: PAYMENT_METHOD_FULL_BALANCE,
    '' => PAYMENT_METHOD_VIA_CASHIER
  }.freeze

  PAYMENT_METHODS = [
    PAYMENT_METHOD_VA,
    PAYMENT_METHOD_CC,
    PAYMENT_METHOD_GOPAY,
    PAYMENT_METHOD_OVO,
    PAYMENT_METHOD_DANA,
    PAYMENT_METHOD_LINKAJA,
    PAYMENT_METHOD_SHOPEEPAY,
    PAYMENT_METHOD_SAKUKU,
    PAYMENT_METHOD_QRIS,
    PAYMENT_METHOD_VIA_CASHIER,
    PAYMENT_METHOD_FULL_BALANCE
  ].freeze

  before_validation :assign_charge_to_purchaser

  validate :validate_country, on: :update
  validate :validate_via_cashier_if_foodcourt, on: :update
  validate :validate_payment_method_location_rules
  before_save :set_zero_fee_for_foreign_country

  audited

  def initialize(params = {})
    super

    self.payment_method_location_rules = default_payment_method_location_rules if payment_method_location_rules.blank?
  end

  def self.ransackable_scopes(_auth_object = nil)
    %i[brand_name_contains]
  end

  def self.brand_name_contains(value)
    joins("LEFT JOIN brands ON dine_in_fee_setupable_id = brands.id AND dine_in_fee_setupable_type = 'Brand'")
      .where('lower(brands.name) LIKE lower(?)', "%#{value}%")
  end

  def by_fee_selector(fee_selector)
    prefix = FEE_SELECTOR_MAP[fee_selector]

    raise Errors::InvalidParamsError, I18n.t('delivery.payments.errors.payment_method_not_supported') if prefix.blank?

    if [PAYMENT_METHOD_VIA_CASHIER, PAYMENT_METHOD_FULL_BALANCE].include?(prefix)
      return {
        enable: public_send("#{prefix}_enable"),
        percentage_rate: public_send("#{prefix}_platform_fee_percentage_rate").to_f,
        flat_rate: public_send("#{prefix}_platform_fee_flat_rate").to_f
      }
    end

    {
      enable: public_send("#{prefix}_enable"),
      percentage_rate: (public_send("#{prefix}_pg_fee_percentage_rate") + public_send("#{prefix}_platform_fee_percentage_rate")).to_f,
      flat_rate: (public_send("#{prefix}_pg_fee_flat_rate") + public_send("#{prefix}_platform_fee_flat_rate")).to_f
    }
  end

  def by_platform_fee_selector(fee_selector)
    prefix = FEE_SELECTOR_MAP[fee_selector]

    raise Errors::InvalidParamsError, I18n.t('delivery.payments.errors.payment_method_not_supported') if prefix.blank?

    {
      enable: public_send("#{prefix}_enable"),
      percentage_rate: public_send("#{prefix}_platform_fee_percentage_rate").to_f,
      flat_rate: public_send("#{prefix}_platform_fee_flat_rate").to_f
    }
  end

  def by_pg_fee_selector(fee_selector)
    prefix = FEE_SELECTOR_MAP[fee_selector]

    raise Errors::InvalidParamsError, I18n.t('delivery.payments.errors.payment_method_not_supported') if prefix.blank?

    if [PAYMENT_METHOD_VIA_CASHIER, PAYMENT_METHOD_FULL_BALANCE].include?(prefix)
      return {
        enable: public_send("#{prefix}_enable"),
        percentage_rate: 0.to_f,
        flat_rate: 0.to_f
      }
    end

    {
      enable: public_send("#{prefix}_enable"),
      percentage_rate: public_send("#{prefix}_pg_fee_percentage_rate").to_f,
      flat_rate: public_send("#{prefix}_pg_fee_flat_rate").to_f
    }
  end

  def set_zero_fee_for_foreign_country
    return unless ALLOWED_COUNTRY.exclude?(dine_in_fee_setupable.country)

    # set all third party payment to false
    self.va_enable = false
    self.cc_enable = false
    self.gopay_enable = false
    self.ovo_enable = false
    self.dana_enable = false
    self.linkaja_enable = false
    self.shopeepay_enable = false
    self.sakuku_enable = false
    self.qris_enable = false

    # override default value in db, set to 0
    self.platform_fee_flat_rate = 0
    self.platform_fee_percentage_rate = 0
    self.via_cashier_platform_fee_flat_rate = 0
    self.full_balance_platform_fee_flat_rate = 0
  end

  private

  def default_payment_method_location_rules
    {
      PAYMENT_METHOD_VA => default_location_rule,
      PAYMENT_METHOD_CC => default_location_rule,
      PAYMENT_METHOD_GOPAY => default_location_rule,
      PAYMENT_METHOD_OVO => default_location_rule,
      PAYMENT_METHOD_DANA => default_location_rule,
      PAYMENT_METHOD_LINKAJA => default_location_rule,
      PAYMENT_METHOD_SHOPEEPAY => default_location_rule,
      PAYMENT_METHOD_SAKUKU => default_location_rule,
      PAYMENT_METHOD_QRIS => default_location_rule,
      PAYMENT_METHOD_VIA_CASHIER => default_location_rule,
      PAYMENT_METHOD_FULL_BALANCE => default_location_rule
    }
  end

  def default_location_rule
    {
      is_select_all_location: true,
      location_ids: [],
      exclude_location_ids: []
    }
  end

  def validate_payment_method_location_rules
    return if payment_method_location_rules.blank?

    validate_payment_method_keys
    validate_each_payment_method_rule
  end

  def validate_payment_method_keys
    missing_keys = PAYMENT_METHODS - payment_method_location_rules.keys

    return if missing_keys.blank?

    errors.add(
      :payment_method_location_rules,
      I18n.t('dine_in_fee_setting.errors.missing_payment_method_keys', missing_keys: missing_keys.join(', '))
    )
  end

  def validate_each_payment_method_rule
    payment_method_location_rules.each do |payment_method, rule|
      unless rule.is_a?(Hash)
        errors.add(
          :payment_method_location_rules,
          I18n.t('dine_in_fee_setting.errors.payment_method_location_rule_must_be_hash', payment_method: payment_method)
        )
        next
      end

      validate_location_rule_fields(payment_method, rule)
    end
  end

  def validate_location_rule_fields(payment_method, rule)
    missing_keys = ['exclude_location_ids', 'is_select_all_location', 'location_ids'] - rule.keys

    if missing_keys.present?
      errors.add(
        :payment_method_location_rules,
        I18n.t('dine_in_fee_setting.errors.missing_location_rule_fields', payment_method: payment_method, missing_keys: missing_keys.join(', '))
      )
    end

    unless rule['is_select_all_location'].in?([true, false])
      errors.add(
        :payment_method_location_rules,
        I18n.t('dine_in_fee_setting.errors.is_select_all_location_must_be_boolean', payment_method: payment_method)
      )
    end

    unless rule['location_ids'].is_a?(Array)
      errors.add(
        :payment_method_location_rules,
        I18n.t('dine_in_fee_setting.errors.location_ids_must_be_array', payment_method: payment_method)
      )
    end

    unless rule['exclude_location_ids'].is_a?(Array)
      errors.add(
        :payment_method_location_rules,
        I18n.t('dine_in_fee_setting.errors.exclude_location_ids_must_be_array', payment_method: payment_method)
      )
    end
  end

  def any_payment_methods_enabled?
    va_enable? || cc_enable? || gopay_enable? || ovo_enable? || dana_enable? || linkaja_enable? || shopeepay_enable? || sakuku_enable? || qris_enable
  end

  def validate_country
    return unless any_payment_methods_enabled?

    errors.add(:base, I18n.t('dine_in_fee_setting.errors.unsupported_payment_setting')) if ALLOWED_COUNTRY.exclude?(dine_in_fee_setupable.country)
  end

  def validate_via_cashier_if_foodcourt
    return unless dine_in_fee_setupable.instance_of?(Brand)
    return unless dine_in_fee_setupable.is_foodcourt
    return if !via_cashier_enable && !via_cashier_charge_to_purchaser

    errors.add(:base,
               I18n.t('dine_in_fee_setting.errors.via_cashier_should_disabled_for_foodcourt'))
  end

  # rubocop:disable Metrics/AbcSize
  def assign_charge_to_purchaser
    self.va_pg_fee_charge_to_purchaser = va_charge_to_purchaser if va_pg_fee_charge_to_purchaser.nil?
    self.va_platform_fee_charge_to_purchaser = va_charge_to_purchaser if va_platform_fee_charge_to_purchaser.nil?

    self.cc_pg_fee_charge_to_purchaser = cc_charge_to_purchaser if cc_pg_fee_charge_to_purchaser.nil?
    self.cc_platform_fee_charge_to_purchaser = cc_charge_to_purchaser if cc_platform_fee_charge_to_purchaser.nil?

    self.gopay_pg_fee_charge_to_purchaser = gopay_charge_to_purchaser if gopay_pg_fee_charge_to_purchaser.nil?
    self.gopay_platform_fee_charge_to_purchaser = gopay_charge_to_purchaser if gopay_platform_fee_charge_to_purchaser.nil?

    self.ovo_pg_fee_charge_to_purchaser = ovo_charge_to_purchaser if ovo_pg_fee_charge_to_purchaser.nil?
    self.ovo_platform_fee_charge_to_purchaser = ovo_charge_to_purchaser if ovo_platform_fee_charge_to_purchaser.nil?

    self.dana_pg_fee_charge_to_purchaser = dana_charge_to_purchaser if dana_pg_fee_charge_to_purchaser.nil?
    self.dana_platform_fee_charge_to_purchaser = dana_charge_to_purchaser if dana_platform_fee_charge_to_purchaser.nil?

    self.linkaja_pg_fee_charge_to_purchaser = linkaja_charge_to_purchaser if linkaja_pg_fee_charge_to_purchaser.nil?
    self.linkaja_platform_fee_charge_to_purchaser = linkaja_charge_to_purchaser if linkaja_platform_fee_charge_to_purchaser.nil?

    self.shopeepay_pg_fee_charge_to_purchaser = shopeepay_charge_to_purchaser if shopeepay_pg_fee_charge_to_purchaser.nil?
    self.shopeepay_platform_fee_charge_to_purchaser = shopeepay_charge_to_purchaser if shopeepay_platform_fee_charge_to_purchaser.nil?

    self.sakuku_pg_fee_charge_to_purchaser = sakuku_charge_to_purchaser if sakuku_pg_fee_charge_to_purchaser.nil?
    self.sakuku_platform_fee_charge_to_purchaser = sakuku_charge_to_purchaser if sakuku_platform_fee_charge_to_purchaser.nil?

    self.qris_pg_fee_charge_to_purchaser = qris_charge_to_purchaser if qris_pg_fee_charge_to_purchaser.nil?
    self.qris_platform_fee_charge_to_purchaser = qris_charge_to_purchaser if qris_platform_fee_charge_to_purchaser.nil?

    self.via_cashier_pg_fee_charge_to_purchaser = via_cashier_charge_to_purchaser if via_cashier_pg_fee_charge_to_purchaser.nil?
    self.via_cashier_platform_fee_charge_to_purchaser = via_cashier_charge_to_purchaser if via_cashier_platform_fee_charge_to_purchaser.nil?

    self.full_balance_pg_fee_charge_to_purchaser = full_balance_charge_to_purchaser if full_balance_pg_fee_charge_to_purchaser.nil?
    self.full_balance_platform_fee_charge_to_purchaser = full_balance_charge_to_purchaser if full_balance_platform_fee_charge_to_purchaser.nil?
  end
  # rubocop:enable Metrics/AbcSize
end
