module Restaurant
  module Concerns
    module OrderProductCount
      extend ActiveSupport::Concern

      # Explnation of each function
      # OPC stands for Order Product Count
      #
      # For location that send order
      # all_opc = order status pending, processing, closed di sum order_qty
      # pending_opc = pending not approve order status pending di sum order_qty
      # processing_not_sent_opc = order status processing, but dont have delivery
      # closed_in_transit_opc = order status closed, delivery sent sum delivery_qty
      # processing_in_transit_opc = order status processing,  delivery sent sum delivery_qty
      # item_outgoing_opc = order status processing, closed, all delivery sum delivery_qty
      # item_received_opc = order status processing, closed, delivery delivered , incomplete sum receive_qty
      # item_rejected_opc = order status processing dan closed, delivery status incomplete sum(delivered - receive)
      # item_incomplete_opc = order status processing, delivery status incomplete sum(delivered - receive)
      # cancelled_opc = order status closed sum (open_qty)
      # void_opc = order_status void, sum order_qty
      # incoming_qty_opc = closed_in_transit_opc + processing_not_sent_opc + processing_in_transit_opc
      #
      # For location that receive order
      # back_order_opc = order that not being fullfiled but accepted
      # end

      # rubocop:disable Metrics/ParameterLists
      def all_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .where(order_transactions: { status: [OrderTransaction.statuses[:pending],
                                                                             OrderTransaction.statuses[:processing],
                                                                             OrderTransaction.statuses[:closed]] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(product_qty,0)').to_d
      end

      def pending_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:pending] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(product_qty,0)').to_d
      end

      def third_party_pending_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction, order_transaction_line_fulfillments: :order_transaction)
                                       .where(order_transactions_order_transaction_lines: { status: OrderTransaction.statuses[:processing] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum do |line|
          line.order_transaction_line_fulfillments.sum { |fulfillment_line| fulfillment_line.open_qty.to_d }
        end
      end

      # rubocop:disable Metrics/AbcSize
      def processing_not_sent_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        processing_qty = 0

        query = order_transaction_lines.joins(:order_transaction)
                                       .includes(:delivery_transaction_lines, order_transaction_line_fulfillments: :order_transaction)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:processing] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.each do |order_line|
          sent_qty = order_line.delivery_transaction_lines.sum(&:delivered_qty)
          fulfillment_qty = order_line.order_transaction_line_fulfillments
                                      .map { |line| (line.order_transaction.processing? ? line.open_qty.to_d : 0.to_d) + line.delivered_qty.to_d }
                                      .compact.sum
          incomplete_delivery = order_line.delivery_transaction_lines.map do |delivery|
            if delivery.received_quantity >= 0 && delivery.delivery_transaction.status != 'sent'
              delivery.delivered_qty.to_d - delivery.received_quantity.to_d
            else
              0
            end
          end.sum

          base_processing_qty = (order_line.product_qty.to_d - sent_qty.to_d +
                                 incomplete_delivery.to_d - fulfillment_qty.to_d)

          processing_qty += order_line.product_unit_conversion_qty.to_d * base_processing_qty

          if brand.procurement_allow_order_fulfill_after_return
            processing_qty += order_line.product_unit_conversion_qty.to_d * order_line.accepted_returned_qty
          end
        end

        processing_qty
      end
      # rubocop:enable Metrics/AbcSize

      def processing_in_transit_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .joins(:delivery_transactions)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:processing] },
                                              delivery_transactions: { status: DeliveryTransaction.statuses[:sent] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(delivered_qty,0)').to_d
      end

      def processing_in_transit_opc_readonly(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = ::Report::Models::OrderTransactionLine
                .joins(:order_transaction)
                .joins(:delivery_transactions)
                .where(product_id: id)
                .where(order_transactions: { status: OrderTransaction.statuses[:processing] },
                       delivery_transactions: { status: DeliveryTransaction.statuses[:sent] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(delivered_qty,0)').to_d
      end

      def closed_in_transit_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .joins(:delivery_transactions)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:closed] },
                                              delivery_transactions: { status: DeliveryTransaction.statuses[:sent] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(delivered_qty,0)').to_d
      end

      def closed_in_transit_opc_readonly(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = ::Report::Models::OrderTransactionLine.joins(:order_transaction)
                                                      .joins(:delivery_transactions)
                                                      .where(product_id: id)
                                                      .where(order_transactions: { status: OrderTransaction.statuses[:closed] },
                                                             delivery_transactions: { status: DeliveryTransaction.statuses[:sent] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(delivered_qty,0)').to_d
      end

      def item_outgoing_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .left_outer_joins(:delivery_transactions)
                                       .includes(order_transaction_line_fulfillments: :delivery_transaction_lines)
                                       .where(order_transactions: { status: [OrderTransaction.statuses[:processing],
                                                                             OrderTransaction.statuses[:closed]] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        order_transaction_line_ids = query.pluck(:id).uniq

        OrderTransactionLine.where(id: order_transaction_line_ids).map do |order_line|
          delivery_fulfillments_qty = order_line.order_transaction_line_fulfillments
                                                .map do |line|
            line.delivery_transaction_lines.sum do |delivery_line|
              delivery_line.delivered_qty.to_d
            end
          end.sum

          order_line.product_unit_conversion_qty.to_d * (order_line.delivery_transaction_lines.sum do |line|
                                                           line.delivered_qty.to_d
                                                         end + delivery_fulfillments_qty)
        end.sum
      end

      def item_received_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .joins(:delivery_transactions)
                                       .where(order_transactions: { status: [OrderTransaction.statuses[:processing],
                                                                             OrderTransaction.statuses[:closed]] },
                                              delivery_transactions: { status: [OrderTransaction.statuses[:delivered],
                                                                                DeliveryTransaction.statuses[:incomplete]] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(received_quantity,0)').to_d
      end

      def item_rejected_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .joins(:delivery_transactions)
                                       .where(order_transactions: { status: [OrderTransaction.statuses[:processing],
                                                                             OrderTransaction.statuses[:closed]] },
                                              delivery_transactions: { status: [DeliveryTransaction.statuses[:delivered],
                                                                                DeliveryTransaction.statuses[:incomplete]] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * (COALESCE(delivered_qty,0) - COALESCE(received_quantity,0))').to_d +
          returned_opc(location, start_date, end_date, location_to) +
          waste_opc(location, start_date, end_date, location_to)
      end

      def returned_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = return_transaction_lines.left_outer_joins(delivery_return: :wastes)
        query = query.where('delivery_returns.status = ?', Restaurant::Models::DeliveryReturn.statuses['accepted']).where('wastes.id IS NULL')
        query = query.where(delivery_returns: { location_from: location }) if location.present?
        query = query.where(delivery_returns: { location_to: location_to }) if location_to.present?
        query = query.where('delivery_returns.return_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('delivery_returns.return_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('return_transaction_lines.product_unit_conversion_qty * (COALESCE(quantity,0))').to_d
      end

      def waste_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = waste_lines.left_outer_joins(:delivery_return)
        query = query.where('delivery_returns.status = ?', Restaurant::Models::DeliveryReturn.statuses['accepted'])
        query = query.where(delivery_returns: { location_from: location }) if location.present?
        query = query.where(delivery_returns: { location_to: location_to }) if location_to.present?
        query = query.where('delivery_returns.return_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('delivery_returns.return_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('waste_lines.product_unit_conversion_qty * (COALESCE(quantity,0))').to_d
      end

      def item_incomplete_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .left_outer_joins(:delivery_transactions)
                                       .where(order_transactions: { status: [OrderTransaction.statuses[:processing],
                                                                             OrderTransaction.statuses[:closed]] },
                                              delivery_transactions: { status: [DeliveryTransaction.statuses[:incomplete]] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * (COALESCE(delivered_qty,0) - COALESCE(received_quantity,0))').to_d
      end

      def cancelled_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .left_outer_joins(:delivery_transactions)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:closed] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty *
                  (COALESCE(product_qty,0) - COALESCE(delivered_qty,0) +
                  (COALESCE(delivered_qty,0) - COALESCE(received_quantity,0)))').to_d
      end

      def void_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:void] })
        query = query.where(order_transactions: { location_from: location }) if location.present?
        query = query.where(order_transactions: { location_to: location_to }) if location_to.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty * COALESCE(product_qty,0)').to_d
      end

      def incoming_qty_opc(location = nil, start_date = nil, end_date = nil, location_to = nil)
        closed_in_transit_opc(location, start_date, end_date, location_to) +
          processing_not_sent_opc(location, start_date, end_date, location_to) +
          processing_in_transit_opc(location, start_date, end_date, location_to)
      end

      def incoming_qty_opc_readonly(location = nil, start_date = nil, end_date = nil, location_to = nil)
        closed_in_transit_opc_readonly(location, start_date, end_date, location_to) +
          processing_not_sent_opc(location, start_date, end_date, location_to) +
          processing_in_transit_opc_readonly(location, start_date, end_date, location_to)
      end

      # For location that receive order
      def back_order_opc(location = nil, start_date = nil, end_date = nil, location_from = nil)
        query = order_transaction_lines.joins(:order_transaction)
                                       .left_outer_joins(:delivery_transactions)
                                       .where(order_transactions: { status: OrderTransaction.statuses[:processing] })
        query = query.where(order_transactions: { location_to: location }) if location.present?
        query = query.where(order_transactions: { location_from: location_from }) if location_from.present?
        query = query.where('order_transactions.order_date >= ?', start_date.to_date.strftime) if start_date.present?
        query = query.where('order_transactions.order_date <= ?', end_date.to_date.strftime) if end_date.present?

        query.sum('product_unit_conversion_qty *
                  (COALESCE(product_qty,0) - COALESCE(delivered_qty,0) +
                  (COALESCE(delivered_qty,0) - COALESCE(received_quantity,0)))').to_d
      end
      # rubocop:enable Metrics/ParameterLists

      def incoming_qty_opc_bulk?(location_ids)
        data = {}
        # pending
        pending = ::Report::Models::OrderTransactionLine.where(product_id: id)
                                                        .joins(:order_transaction)
                                                        .where(order_transactions: { status: OrderTransaction.statuses[:pending],
                                                                                     location_from_type: 'Location',
                                                                                     brand_id: brand_id,
                                                                                     location_from_id: location_ids })
                                                        .group('order_transactions.location_from_id')
                                                        .sum('product_qty')

        # processing
        processing = ::Report::Models::OrderTransactionLine.where(product_id: id)
                                                           .joins(:order_transaction)
                                                           .where(order_transactions: { status: OrderTransaction.statuses[:processing],
                                                                                        location_from_type: 'Location',
                                                                                        brand_id: brand_id,
                                                                                        location_from_id: location_ids })
                                                           .group('order_transactions.location_from_id')
                                                           .sum('product_qty')

        # in_transit
        in_transit = ::Report::Models::OrderTransactionLine.where(product_id: id)
                                                           .joins(:order_transaction)
                                                           .joins(:delivery_transactions)
                                                           .where(order_transactions: { status: OrderTransaction.statuses[:processing],
                                                                                        location_from_type: 'Location',
                                                                                        brand_id: brand_id,
                                                                                        location_from_id: location_ids })
                                                           .group('order_transactions.location_from_id')
                                                           .sum('delivered_qty')

        location_ids.each do |l_id|
          data[l_id] = (pending[l_id].to_d + processing[l_id].to_d + in_transit[l_id].to_d).positive?
        end

        data
      end
    end
  end
end
