class Restaurant::Services::Procurement::OrderLineDetailResponseBuilder
  attr_reader :order,
              :order_transaction_line,
              :show_delivered_qty,
              :can_manage_price_and_discount,
              :show_product_details,
              :current_user,
              :location_from_id,
              :update_to_latest,
              :is_order_fulfillment,
              :show_vendor_products,
              :show_permission

  private :order,
          :order_transaction_line,
          :show_delivered_qty,
          :can_manage_price_and_discount,
          :show_product_details,
          :current_user,
          :location_from_id,
          :update_to_latest,
          :is_order_fulfillment,
          :show_vendor_products,
          :show_permission

  def initialize(params:)
    @order = params[:order]
    @order_transaction_line = params[:order_transaction_line]
    @show_delivered_qty = params[:show_delivered_qty]
    @can_manage_price_and_discount = params[:can_manage_price_and_discount]
    @can_see_payment_status = params[:can_see_payment_status]
    @show_product_details = params[:show_product_details]
    @current_user = params[:current_user]
    @location_from_id = params[:location_from_id]
    @update_to_latest = params[:update_to_latest]
    @is_order_fulfillment = order.order_fulfillment?
    @show_vendor_products = params.fetch(:show_vendor_products, false)
    @show_permission = params.fetch(:show_permission, false)
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def call
    order_transaction_line.update_to_latest if update_to_latest
    response = order_transaction_line.attributes.with_indifferent_access
    response = response.except('product_buy_price', 'discount', 'total_amount', 'tax_rate') unless can_manage_price_and_discount

    vendor_product = find_vendor_product

    response[:product] = order_transaction_line.product_object_builder(order)
    response[:product_unit] = order_transaction_line.product_unit_object_builder
    response[:tax] = order_transaction_line.tax_object_builder if order_transaction_line.tax_id.present?

    if current_user.present? && show_product_details
      to_vendor = order.to_vendor?

      product_query = ProductQuery.new({
                                         current_user: current_user,
                                         location_id: location_from_id,
                                         status: 'activated',
                                         presentation: 'internal_price_with_product_setting_location',
                                         any_external_vendor_type: to_vendor,
                                         vendor_id: to_vendor ? order.location_to_id : nil
                                       })
      product_detail = product_query.generate_filter_response(order_transaction_line.product)

      response[:product].merge!(product_detail)
    end

    response[:vendor_product] = vendor_product if show_vendor_products && order.external?
    if can_manage_price_and_discount
      response[:total_amount] = if show_delivered_qty
                                  order_transaction_line.subtotal_line_payment_amount_after_discount_exclude_promo_total_order.to_d
                                else
                                  order_transaction_line.total_amount_without_global_promo.to_d
                                end
      response[:discount_amount] = if show_delivered_qty
                                     order_transaction_line.discount_exclude_promo_total_order_by_delivered_quantity
                                   else
                                     order_transaction_line.discount_item_exclude_promo_total_order
                                   end
    end

    if show_delivered_qty
      response[:delivered_qty] = order_transaction_line.total_delivered_or_received_quantity_including_fulfillments.to_d
    else
      response[:open_qty] = order_transaction_line.open_qty.to_d.positive? ? order_transaction_line.open_qty.to_d : 0.to_d
    end

    response[:can_input_custom_price] = order_transaction_line.can_input_custom_price? if show_permission

    response[:can_edit_price_by_franchisor] = order_transaction_line.price_editable_by_franchisor?(current_user)
    response.delete(:custom_product_buy_price_was_set_by_franchisor)

    response.with_indifferent_access
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  private

  def find_vendor_product
    return unless order.external?

    vendor_products = order_transaction_line.product.vendor_products
    return if vendor_products.blank?

    vendor_products.detect do |vendor_product|
      vendor_product.vendor_id == order.location_to_id && vendor_product.product_unit_id == order_transaction_line.product_unit_id
    end
  end
end
