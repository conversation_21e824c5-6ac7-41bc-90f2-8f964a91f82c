class Restaurant::Services::Procurement::OrderLineFulfillmentDetailResponseBuilder
  attr_reader :order,
              :order_transaction_line,
              :show_delivered_qty,
              :can_manage_price_and_discount,
              :current_user,
              :location_from_id,
              :update_to_latest,
              :is_order_fulfillment,
              :is_fulfillable_order_line,
              :locations,
              :unit_conversion_show,
              :product_unit_locations_params,
              :show_vendor_products,
              :vendor_location_id

  private :order,
          :order_transaction_line,
          :show_delivered_qty,
          :can_manage_price_and_discount,
          :current_user,
          :location_from_id,
          :update_to_latest,
          :is_order_fulfillment,
          :is_fulfillable_order_line,
          :locations,
          :unit_conversion_show,
          :product_unit_locations_params,
          :show_vendor_products,
          :vendor_location_id

  def initialize(params:)
    @order = params[:order]
    @order_transaction_line = params[:order_transaction_line]
    @show_delivered_qty = params[:show_delivered_qty]
    @can_manage_price_and_discount = params[:can_manage_price_and_discount]
    @current_user = params[:current_user]
    @location_from_id = params[:location_from_id]
    @update_to_latest = params[:update_to_latest]
    @is_fulfillable_order_line = params.fetch(:is_fulfillable_order_line, false)
    @is_order_fulfillment = order.order_fulfillment?
    @locations = {
      location_from: @is_order_fulfillment ? order.location_from : order.location_to,
      location_to: @is_order_fulfillment ? order.location_to : params[:location_to]
    }
    @vendor_location_id = if params[:location_to].present? && params[:location_to].is_a?(Vendor)
                            params[:location_to].id
                          elsif order.external?
                            order.location_to_id
                          end
    @product_unit_locations_params = {
      location_from_id: @locations[:location_from].id,
      location_from_type: @locations[:location_from].model_name.name,
      location_to_id: @locations[:location_to]&.id,
      location_to_type: @locations[:location_to]&.model_name&.name
    }
    @unit_conversion_show = Api::ProductUnitConversionsPolicy.new(current_user, nil).show?
    @show_vendor_products = params.fetch(:show_vendor_products, false)
  end

  # rubocop:disable Metrics/AbcSize
  def call
    order_transaction_line.update_to_latest if update_to_latest
    response = order_transaction_line.attributes.with_indifferent_access
    response = response.except('product_buy_price', 'discount', 'total_amount', 'tax_rate') unless can_manage_price_and_discount

    vendor_product = find_vendor_product
    response = assign_additional_attributes_to_object(response, vendor_product)
    response = assign_values_to_additional_order_line_fulfillment(response) if is_fulfillable_order_line

    response[:vendor_product] = vendor_product if show_vendor_products && vendor_location_id.present?
    if can_manage_price_and_discount
      response[:total_amount] = if show_delivered_qty
                                  order_transaction_line.subtotal_line_payment_amount_after_discount_exclude_promo_total_order.to_d
                                else
                                  order_transaction_line.total_amount_without_global_promo.to_d
                                end
      response[:discount_amount] = if show_delivered_qty
                                     order_transaction_line.discount_exclude_promo_total_order_by_delivered_quantity
                                   else
                                     order_transaction_line.discount_item_exclude_promo_total_order
                                   end
    end

    if show_delivered_qty
      response[:delivered_qty] = order_transaction_line.total_delivered_or_received_quantity_including_fulfillments.to_d
    else
      response[:open_qty] = order_transaction_line.open_qty.to_d.positive? ? order_transaction_line.open_qty.to_d : 0.to_d
    end

    response[:can_input_custom_price] = false

    response.delete(:custom_product_buy_price_was_set_by_franchisor)

    response.with_indifferent_access
  end
  # rubocop:enable Metrics/AbcSize

  private

  def find_vendor_product
    return if vendor_location_id.blank?

    vendor_products = order_transaction_line.product.vendor_products
    return if vendor_products.blank?

    vendor_products.detect do |vendor_product|
      vendor_product.vendor_id == vendor_location_id && vendor_product.product_unit_id == order_transaction_line.product_unit_id
    end
  end

  def build_errors
    order_transaction_line.valid_to_create_fulfillment?(locations: locations, order_fulfillment_location_to_exist: true)
  end

  # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
  def assign_additional_attributes_to_object(response, vendor_product)
    response[:product] = order_transaction_line.product_object_builder(order)
    response[:product_unit] = order_transaction_line.product_unit_object_builder
    if order_transaction_line.tax_id.present? && order.brand.procurement_setting.use_tax_vendor_procurement && vendor_location_id.present?
      response[:tax] =
        order_transaction_line.tax_object_builder
    else
      response = response.except(:tax, :tax_id, :tax_rate, :tax_name, :tax_amount)
    end

    errors = build_errors

    available_product_unit_ids = Restaurant::Services::Procurement::AvailableProductUnits.new(
      location_from: locations[:location_from],
      location_to: locations[:location_to],
      price_show: can_manage_price_and_discount,
      unit_conversion_show: unit_conversion_show,
      params: product_unit_locations_params,
      order_transaction_line: order_transaction_line
    ).call

    if available_product_unit_ids.exclude?(order_transaction_line.product_unit_id)
      errors << I18n.t('orders.order_transaction_lines.errors.product_unit_is_not_available')
    end

    response[:errors] = !is_order_fulfillment || (is_order_fulfillment && order.pending?) ? errors : []

    if can_manage_price_and_discount
      new_order_fulfillment_line = is_fulfillable_order_line || !is_order_fulfillment
      response[:product_buy_price] = if new_order_fulfillment_line && vendor_product.present?
                                       vendor_product.sell_price
                                     elsif new_order_fulfillment_line && !locations[:location_from].is_franchise?
                                       nil
                                     else
                                       response[:product_buy_price]
                                     end

      response[:discount_amount] = order_transaction_line.discount_amount

      response[:total_amount] = if new_order_fulfillment_line
                                  '0.0'
                                else
                                  response[:total_amount]
                                end
    end

    response[:parent_order_line_id] = order_transaction_line.generate_parent_order_line_id
    order_transaction = order_transaction_line.order_transaction
    response[:maximum_quantity] = if order_transaction.order_fulfillment?
                                    order_transaction_line.with_multibrand_parent_order_line.open_qty.to_d + order_transaction_line.product_qty.to_d
                                  else
                                    order_transaction_line.open_qty.to_d
                                  end
    response
  end
  # rubocop:enable Metrics/AbcSize, Metrics/MethodLength

  def assign_values_to_additional_order_line_fulfillment(response)
    response[:id] = nil
    response[:product_qty] = nil
    response[:discount_amount] = ''

    response.except(:created_at, :updated_at)
  end
end
