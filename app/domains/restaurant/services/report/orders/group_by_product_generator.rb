# Create report details when filter by product
class Restaurant::Services::Report::Orders::GroupByProductGenerator
  include Restaurant::Modules::Report::CogsCalculator
  include Param<PERSON><PERSON><PERSON>

  def initialize(params:)
    @brand = params[:brand]
    @is_export = params[:is_export]
    @orders = params[:orders]
    @can_manage_price_and_discount = params[:can_manage_price_and_discount]
    @order_filter_params = params[:order_filter_params]
    @repeat_line = params[:repeat_line]
    @user = @order_filter_params[:current_user]
    @product_ids = multiparams_parse(@order_filter_params[:product_ids])
    @can_view_stat = @order_filter_params[:can_view_stat]
    @can_view_previous_price = @order_filter_params[:can_view_previous_price]
    @order_nature = @order_filter_params['order_nature']
    @show_request_delivery_date = params[:show_request_delivery_date]
    @price_for_procurement_internal = params[:price_for_procurement_internal]
  end

  def call
    build_report_details
  end

  private

  # rubocop:disable Metrics/MethodLength
  def build_report_details
    report_details = []

    previous_data = {
      delivery_id: nil,
      delivery_line_id: nil,
      order_id: nil,
      order_line_id: nil
    }

    page_size = 2000
    current_page = 1
    completed = false

    loop do
      orders = ::Report::Models::OrderTransaction.preload(:location_to, :location_from)
                                                 .includes({ brand: :procurement_setting },
                                                           { order_transaction_lines: %i[delivery_transaction_lines
                                                                                         order_transaction_line_fulfillments] })
                                                 .where(id: @orders.map(&:id))
                                                 .order(order_date: :desc, id: :desc)
                                                 .page(current_page).per(page_size)
      completed = true if orders.blank?
      break if completed

      order_lines = order_transaction_lines(orders.map(&:id))

      if @order_nature == 'outgoing'
        collect_previous_prices(orders)
        collect_avg_3_months_prices(orders)
      end

      orders.map do |order|
        process_per_order(order, report_details, previous_data, order_lines)
      end

      current_page += 1
    end

    report_details
  end
  # rubocop:enable Metrics/MethodLength

  def process_per_order(order, report_details, previous_data, order_lines)
    order_lines.filter { |order_line| order_line.order_transaction_id == order.id }.sort_by(&:id).map do |order_line|
      data = if order_line.delivery_transaction_lines.size.positive?
               build_report_details_with_deliveries(report_details, order, order_line, previous_data)
             else
               build_report_details_without_deliveries(report_details, order, order_line, previous_data)
             end

      report_details = data[:report_details]
      previous_data = data[:previous_data]
    end
  end

  def order_transaction_lines(order_ids)
    order_lines = ::Report::Models::OrderTransactionLine.joins(:product, :order_transaction).where(order_transaction_id: order_ids)
                                                        .includes(:order_transaction_line_fulfillments,
                                                                  { order_transaction: %i[brand location_from location_to] },
                                                                  [{ delivery_transaction_lines: [:delivery_transaction,
                                                                                                  { delivery_return_lines: :delivery_return }] }])
                                                        .order('order_transactions.order_date DESC, order_transactions.order_no DESC')
    order_lines = order_lines.where(product_id: @product_ids) if @product_ids.present?
    order_lines
  end

  def collect_previous_prices(orders)
    @previous_prices = Restaurant::Services::Procurement::OrderLinesLastPriceFinder.new(order_lines_ids: orders.order_transaction_lines_ids).call
  end

  def collect_avg_3_months_prices(orders)
    @aggregated_lines = Restaurant::Services::Procurement::OrderLinesAggregatePriceFinder
                        .new(order_lines_ids: orders.order_transaction_lines_ids)
                        .call
  end

  def build_report_details_with_deliveries(report_details, order, order_line, previous_data)
    order_line.delivery_transaction_lines
              .sort_by(&:updated_at).reverse
              .map do |delivery_line|
      delivery = delivery_line.delivery_transaction
      same_with_previous = skip_line_with_deliveries?(previous_data, order, order_line, delivery, delivery_line)

      report_details << build_order_data(order, order_line, delivery, delivery_line, same_with_previous)

      previous_data = {
        delivery_id: delivery&.id,
        delivery_line_id: delivery_line.id,
        order_id: order.id,
        order_line_id: order_line.id
      }
    end

    return {
      report_details: report_details,
      previous_data: previous_data
    }
  end

  def skip_line_with_deliveries?(previous_data, order, order_line, delivery, delivery_line)
    return automatic_repeat_condition if @repeat_line

    {
      order_id: previous_data[:order_id] == order.id,
      order_line_id: previous_data[:order_line_id] == order_line.id,
      delivery_id: previous_data[:delivery_id] == delivery&.id,
      delivery_line_id: previous_data[:delivery_line_id] == delivery_line.id
    }
  end

  def skip_line_without_deliveries?(previous_data, order, order_line)
    return automatic_repeat_condition if @repeat_line

    {
      order_line_id: previous_data[:order_line_id] == order_line.id,
      order_id: previous_data[:order_id] == order.id,
      delivery_id: false,
      delivery_line_id: false
    }
  end

  def automatic_repeat_condition
    {
      order_line_id: false,
      order_id: false,
      delivery_id: false,
      delivery_line_id: false
    }
  end

  def build_report_details_without_deliveries(report_details, order, order_line, previous_data)
    delivery_nil = nil
    delivery_line_nil = nil
    skip_line_condition = skip_line_without_deliveries?(previous_data, order, order_line)

    report_details << build_order_data(order, order_line, delivery_nil, delivery_line_nil, skip_line_condition)

    previous_data = {
      delivery_id: nil,
      delivery_line_id: nil,
      order_id: order.id,
      order_line_id: order_line.id
    }

    return {
      report_details: report_details,
      previous_data: previous_data
    }
  end

  def build_order_data(order, order_line, delivery, delivery_line, skip_line_condition)
    order_status_name = OrderHelper.get_order_status_name(order)

    order_data = append_data_of_order_information(order, order_status_name, skip_line_condition) +
                 append_data_of_order_line_information(order_line, skip_line_condition) +
                 append_data_of_delivery_line_information(delivery_line) +
                 append_data_of_product_unit_information(order_line, skip_line_condition) +
                 append_avg_months_stats(order_line, skip_line_condition) +
                 append_previous_price(order_line, skip_line_condition) +
                 append_data_of_order_line_price(order_line, skip_line_condition) +
                 append_data_of_order_price(order, skip_line_condition) +
                 append_data_of_order_notes(order, skip_line_condition) +
                 append_data_of_delivery_information(delivery, skip_line_condition)

    order_data.reject(&:blank?)
  end

  def append_data_of_order_information(order, order_status_name, skip_line_condition)
    row = Restaurant::Services::Report::RowBuilder.new

    skip_col = @show_request_delivery_date ? 6 : 5
    return row.add_empty(skip_col, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_id]

    row.add_text(order.order_date.strftime('%d/%m/%Y'))
       .add_url_text("##{order.order_no}", order)
       .add_badge(order_status_name, OrderHelper.get_status_color_code(order.status))

    row.add_text(order.request_delivery_date&.strftime('%d/%m/%Y') || '-') if @show_request_delivery_date

    location_to_with_mask_display = order.with_mask_display_location_to_name(@user)
    if order.with_mask_location_to(@user).present?
      row.add_url_text_by_resource_class(location_to_with_mask_display, order.location_to_id, order.location_to_type)
    else
      row.add_text(location_to_with_mask_display, opacity: nil, size: nil, weight: 500)
    end

    row.add_url_text_by_resource_class(order.location_from_name, order.location_from_id, order.location_from_type)

    row.build
  end

  def append_data_of_order_line_information(order_line, skip_line_condition)
    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(4, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_line_id]

    open_qty = order_line.open_qty.to_d.positive? ? order_line.open_qty.to_d : 0.to_d

    row.add_text(order_line.product_name, size: nil, opacity: nil, weight: 500)
       .add_text(order_line.product_sku, size: nil, opacity: nil, weight: 500)
       .add_quantity(order_line.product_qty, @brand, is_export: @is_export, weight: 500, alignment: 'right', cell_format: :float)
       .add_quantity(open_qty, @brand, is_export: @is_export, weight: 500, alignment: 'right', cell_format: :float)

    row.build
  end

  def append_data_of_delivery_line_information(delivery_line)
    row = Restaurant::Services::Report::RowBuilder.new

    if delivery_line.blank?
      row.add_text('-', alignment: 'right')
         .add_text('-', alignment: 'right')
         .add_text('-', alignment: 'right')
      return row.build
    end

    order_nature = @order_filter_params['order_nature']
    return_qty = if order_nature == Restaurant::Constants::INCOMING
                   delivery_line.preloaded_accepted_returned_qty
                 else
                   delivery_line.preloaded_pending_and_accepted_returned_qty
                 end

    style_options = { weight: 500, cell_format: :float, alignment: 'right' }

    row.add_quantity(delivery_line.delivered_qty, @brand, is_export: @is_export, **style_options)
       .add_quantity(delivery_line.received_quantity, @brand, is_export: @is_export, **style_options)
       .add_quantity(return_qty, @brand, is_export: @is_export, **style_options)

    row.build
  end

  def append_data_of_product_unit_information(order_line, skip_line_condition)
    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_line_id]

    row.add_text(order_line.product_unit_name, opacity: nil, size: nil, weight: 500)

    row.build
  end

  def append_previous_price(order_line, skip_line_condition)
    return [] if @order_nature != 'outgoing'
    return [] unless @can_view_previous_price

    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_line_id]

    line_previous_price = @previous_prices[order_line.id]
    previous_price = if order_line.internal_to_internal? && !order_line.is_multibrand? # Query will just process 0, here we will just hide it.
                       ''
                     elsif line_previous_price.present?
                       line_previous_price[:product_buy_price].to_d.round(2)
                     else
                       0
                     end

    row.add_money(previous_price || '0', @brand, is_export: @is_export, size: nil, opacity: nil)

    row.build
  end

  def append_avg_months_stats(order_line, skip_line_condition)
    return [] if @order_nature != 'outgoing'
    return [] unless @can_view_stat

    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_line_id]

    line_aggregated_lines = @aggregated_lines[order_line.id]
    avg = if order_line.internal_to_internal? && !order_line.is_multibrand? # Query will just process 0, here we will just hide it.
            ''
          elsif line_aggregated_lines.present? && line_aggregated_lines[:qty].present?
            amount = ((line_aggregated_lines[:order_amount].to_d - line_aggregated_lines[:discount_amount].to_d) / line_aggregated_lines[:qty].to_d)
            (amount * line_aggregated_lines[:product_unit_conversion_qty]).round(2)
          else
            0
          end

    row.add_money(avg || '0', @brand, is_export: @is_export, size: nil, opacity: nil)

    row.build
  end

  def append_data_of_order_line_price(order_line, skip_line_condition)
    return [] unless @can_manage_price_and_discount

    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(3, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_line_id]

    price_per_unit = if !order_line.is_multibrand? && order_line.order_transaction.internal_to_internal?
                       find_price_per_unit_for_procurement_internal(
                         order_line: order_line,
                         price_for_procurement_internal: @price_for_procurement_internal
                       )
                     else
                       order_line.display_product_buy_price
                     end

    total_price = if !order_line.is_multibrand? && order_line.order_transaction.internal_to_internal?
                    find_total_price_per_product_for_procurement_internal(order_line, price_per_unit)
                  else
                    order_line.display_total_amount
                  end

    row.add_money(price_per_unit.presence || '-', @brand, is_export: @is_export)
       .add_money(order_line.display_discount_amount, @brand, is_export: @is_export)
       .add_money(total_price.presence || '-', @brand, is_export: @is_export)

    row.build
  end

  def append_data_of_order_price(order, skip_line_condition)
    return [] unless @can_manage_price_and_discount

    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(4, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_id]

    tax_value = if order.display_total_tax == '-' || order.display_total_tax.blank?
                  order.display_total_tax.presence || ''
                else
                  order.display_total_tax
                end

    total_amount_value = if !order.is_multibrand? && order.internal_to_internal?
                           find_total_price_per_order_for_procurement_internal(
                             order: order,
                             price_for_procurement_internal: @price_for_procurement_internal
                           )
                         else
                           order.display_total_amount
                         end.presence || '-'

    shipping_fee_value = order.display_order_shipping_fee.presence || ''
    shipping_fee_value = total_amount_value if total_amount_value.to_f.zero?

    total_order = total_amount_value.to_f + shipping_fee_value.to_f
    total_order = shipping_fee_value if shipping_fee_value == '-'

    row.add_money(tax_value, @brand, is_export: @is_export)
       .add_money(total_amount_value, @brand, is_export: @is_export)
       .add_money(shipping_fee_value, @brand, is_export: @is_export)
       .add_money(total_order, @brand, is_export: @is_export)

    row.build
  end

  def append_data_of_delivery_information(delivery, skip_line_condition)
    row = Restaurant::Services::Report::RowBuilder.new

    if delivery.blank? || skip_line_condition[:delivery_id] && skip_line_condition[:order_id]
      return row.add_empty(5, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build
    end

    row.add_text(delivery.delivery_date.strftime('%d/%m/%Y'))
       .add_url_text_when_object_is_hash("##{delivery.delivery_no}", delivery.id, delivery.class.name, alignment: nil)
       .add_badge(delivery.status_name, DeliveryHelper.get_status_color_code(delivery.status))
       .add_text(delivery.report_pic_fullname)
       .add_text(delivery.received_date&.strftime('%d/%m/%Y'))

    row.build
  end

  def append_data_of_order_notes(order, skip_line_condition)
    row = Restaurant::Services::Report::RowBuilder.new

    return row.add_empty(1, size: 12, opacity: Restaurant::Constants::DEFAULT_OPACITY).build if skip_line_condition[:order_id]

    row.add_text(order.notes.to_s.truncate(100))

    row.build
  end
end
