FactoryBot.define do
  factory :dine_in_fee_setting, class: 'Restaurant::Models::DineInFeeSetting' do
    association :dine_in_fee_setupable, factory: :brand

    # Enable flags
    va_enable { true }
    cc_enable { true }
    gopay_enable { true }
    ovo_enable { true }
    dana_enable { true }
    linkaja_enable { true }
    shopeepay_enable { true }
    sakuku_enable { true }
    qris_enable { true }
    via_cashier_enable { true }
    full_balance_enable { true }

    # Charge to purchaser flags (original)
    va_charge_to_purchaser { true }
    cc_charge_to_purchaser { true }
    gopay_charge_to_purchaser { true }
    ovo_charge_to_purchaser { true }
    dana_charge_to_purchaser { true }
    linkaja_charge_to_purchaser { true }
    shopeepay_charge_to_purchaser { true }
    sakuku_charge_to_purchaser { true }
    qris_charge_to_purchaser { true }
    via_cashier_charge_to_purchaser { true }
    full_balance_charge_to_purchaser { true }

    # PG fee charge to purchaser flags (new)
    va_pg_fee_charge_to_purchaser { true }
    cc_pg_fee_charge_to_purchaser { true }
    gopay_pg_fee_charge_to_purchaser { true }
    ovo_pg_fee_charge_to_purchaser { true }
    dana_pg_fee_charge_to_purchaser { true }
    linkaja_pg_fee_charge_to_purchaser { true }
    shopeepay_pg_fee_charge_to_purchaser { true }
    sakuku_pg_fee_charge_to_purchaser { true }
    qris_pg_fee_charge_to_purchaser { true }
    via_cashier_pg_fee_charge_to_purchaser { true }
    full_balance_pg_fee_charge_to_purchaser { true }

    # Platform fee charge to purchaser flags (new)
    va_platform_fee_charge_to_purchaser { true }
    cc_platform_fee_charge_to_purchaser { true }
    gopay_platform_fee_charge_to_purchaser { true }
    ovo_platform_fee_charge_to_purchaser { true }
    dana_platform_fee_charge_to_purchaser { true }
    linkaja_platform_fee_charge_to_purchaser { true }
    shopeepay_platform_fee_charge_to_purchaser { true }
    sakuku_platform_fee_charge_to_purchaser { true }
    qris_platform_fee_charge_to_purchaser { true }
    via_cashier_platform_fee_charge_to_purchaser { true }
    full_balance_platform_fee_charge_to_purchaser { true }

    # PG fee rates (default values from migration)
    va_pg_fee_flat_rate { 4000.0 }
    va_pg_fee_percentage_rate { 0.0 }
    cc_pg_fee_flat_rate { 2000.0 }
    cc_pg_fee_percentage_rate { 2.9 }
    gopay_pg_fee_flat_rate { 0.0 }
    gopay_pg_fee_percentage_rate { 0.0 }
    ovo_pg_fee_flat_rate { 0.0 }
    ovo_pg_fee_percentage_rate { 0.0 }
    dana_pg_fee_flat_rate { 0.0 }
    dana_pg_fee_percentage_rate { 0.0 }
    linkaja_pg_fee_flat_rate { 0.0 }
    linkaja_pg_fee_percentage_rate { 0.0 }
    shopeepay_pg_fee_flat_rate { 0.0 }
    shopeepay_pg_fee_percentage_rate { 0.0 }
    sakuku_pg_fee_flat_rate { 0.0 }
    sakuku_pg_fee_percentage_rate { 0.0 }
    qris_pg_fee_flat_rate { 0.0 }
    qris_pg_fee_percentage_rate { 0.0 }

    # Platform fee rates
    platform_fee_flat_rate { 1000.0 }
    platform_fee_percentage_rate { 0.0 }
    via_cashier_platform_fee_flat_rate { 1000.0 }
    via_cashier_platform_fee_percentage_rate { 0.0 }
    full_balance_platform_fee_flat_rate { 1000.0 }
    full_balance_platform_fee_percentage_rate { 0.0 }

    trait :with_foreign_country do
      after(:build) do |setting|
        setting.dine_in_fee_setupable.update!(country: 'Singapore')
      end
    end

    trait :with_foodcourt_brand do
      after(:build) do |setting|
        setting.dine_in_fee_setupable.update!(is_foodcourt: true)
      end
    end

    trait :all_payments_disabled do
      va_enable { false }
      cc_enable { false }
      gopay_enable { false }
      ovo_enable { false }
      dana_enable { false }
      linkaja_enable { false }
      shopeepay_enable { false }
      sakuku_enable { false }
      qris_enable { false }
      via_cashier_enable { false }
      full_balance_enable { false }
    end

    trait :separate_charge_settings do
      # Different settings for PG and platform fees
      va_pg_fee_charge_to_purchaser { false }
      va_platform_fee_charge_to_purchaser { true }
      cc_pg_fee_charge_to_purchaser { true }
      cc_platform_fee_charge_to_purchaser { false }
    end
  end
end
