RSpec.describe Restaurant::Models::DineInFeeSetting, type: :model do
  let(:brand) { create(:brand) }
  let(:dine_in_fee_setting) { brand.dine_in_fee_setting }

  describe 'associations' do
    it { should belong_to(:dine_in_fee_setupable) }
  end

  describe 'constants' do
    it 'defines allowed countries' do
      expect(described_class::ALLOWED_COUNTRY).to eq(['Indonesia'])
    end

    it 'defines payment method constants' do
      expect(described_class::PAYMENT_METHOD_VA).to eq('va')
      expect(described_class::PAYMENT_METHOD_CC).to eq('cc')
      expect(described_class::PAYMENT_METHOD_GOPAY).to eq('gopay')
      expect(described_class::PAYMENT_METHOD_OVO).to eq('ovo')
      expect(described_class::PAYMENT_METHOD_DANA).to eq('dana')
      expect(described_class::PAYMENT_METHOD_LINKAJA).to eq('linkaja')
      expect(described_class::PAYMENT_METHOD_SHOPEEPAY).to eq('shopeepay')
      expect(described_class::PAYMENT_METHOD_SAKUKU).to eq('sakuku')
      expect(described_class::PAYMENT_METHOD_QRIS).to eq('qris')
      expect(described_class::PAYMENT_METHOD_VIA_CASHIER).to eq('via_cashier')
      expect(described_class::PAYMENT_METHOD_FULL_BALANCE).to eq('full_balance')
    end

    it 'defines fee selector map' do
      expect(described_class::FEE_SELECTOR_MAP[:virtual_account]).to eq('va')
      expect(described_class::FEE_SELECTOR_MAP[:credit_card]).to eq('cc')
      expect(described_class::FEE_SELECTOR_MAP[:qris]).to eq('qris')
      expect(described_class::FEE_SELECTOR_MAP[:gopay]).to eq('gopay')
      expect(described_class::FEE_SELECTOR_MAP[:ovo]).to eq('ovo')
      expect(described_class::FEE_SELECTOR_MAP[:dana]).to eq('dana')
      expect(described_class::FEE_SELECTOR_MAP[:linkaja]).to eq('linkaja')
      expect(described_class::FEE_SELECTOR_MAP[:shopeepay]).to eq('shopeepay')
      expect(described_class::FEE_SELECTOR_MAP[:sakuku]).to eq('sakuku')
      expect(described_class::FEE_SELECTOR_MAP[:cash]).to eq('via_cashier')
      expect(described_class::FEE_SELECTOR_MAP[:balance]).to eq('full_balance')
      expect(described_class::FEE_SELECTOR_MAP['']).to eq('via_cashier')
    end
  end

  describe 'callbacks' do
    it 'calls assign_charge_to_purchaser before validation' do
      expect(dine_in_fee_setting).to receive(:assign_charge_to_purchaser)
      dine_in_fee_setting.valid?
    end

    it 'calls set_default_payment_method_location_rules before validation' do
      expect(dine_in_fee_setting).to receive(:set_default_payment_method_location_rules)
      dine_in_fee_setting.valid?
    end

    it 'calls set_zero_fee_for_foreign_country before save' do
      expect(dine_in_fee_setting).to receive(:set_zero_fee_for_foreign_country)
      dine_in_fee_setting.save
    end
  end

  describe 'validations' do
    context 'validate_country on update' do
      context 'when brand is in Indonesia' do
        before { brand.update!(country: 'Indonesia') }

        it 'allows payment methods to be enabled' do
          dine_in_fee_setting.update!(va_enable: true, cc_enable: true)
          expect(dine_in_fee_setting).to be_valid
        end
      end

      context 'when brand is not in Indonesia' do
        before { brand.update!(country: 'Singapore') }

        it 'adds error when payment methods are enabled' do
          dine_in_fee_setting.va_enable = true
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:base]).to include(I18n.t('dine_in_fee_setting.errors.unsupported_payment_setting'))
        end
      end
    end

    context 'validate_via_cashier_if_foodcourt on update' do
      let(:foodcourt_brand) { create(:brand, is_foodcourt: true) }
      let(:foodcourt_setting) { foodcourt_brand.dine_in_fee_setting }

      it 'adds error when via_cashier is enabled for foodcourt' do
        foodcourt_setting.via_cashier_enable = true
        foodcourt_setting.via_cashier_charge_to_purchaser = true
        foodcourt_setting.valid?
        expect(foodcourt_setting.errors[:base]).to include(I18n.t('dine_in_fee_setting.errors.via_cashier_should_disabled_for_foodcourt'))
      end

      it 'allows via_cashier to be disabled for foodcourt' do
        foodcourt_setting.update!(via_cashier_enable: false, via_cashier_charge_to_purchaser: false)
        expect(foodcourt_setting).to be_valid
      end
    end
  end

  describe 'initialization' do
    it 'sets default payment_method_location_rules after validation' do
      setting = described_class.new(dine_in_fee_setupable: brand)
      setting.valid?
      expect(setting.payment_method_location_rules).to be_present
      expect(setting.payment_method_location_rules.keys).to match_array(described_class::PAYMENT_METHODS.map(&:to_s))
    end
  end

  describe '#set_default_payment_method_location_rules' do
    context 'when payment_method_location_rules is blank' do
      before do
        dine_in_fee_setting.payment_method_location_rules = nil
      end

      it 'sets default payment method location rules on validation' do
        dine_in_fee_setting.valid?
        expect(dine_in_fee_setting.payment_method_location_rules).to be_present
        expect(dine_in_fee_setting.payment_method_location_rules.keys).to match_array(described_class::PAYMENT_METHODS.map(&:to_s))
      end
    end

    context 'when payment_method_location_rules is already set' do
      let(:custom_rules) { { 'va' => { 'is_select_all_location' => false, 'location_ids' => [1, 2], 'exclude_location_ids' => [] } } }

      before do
        dine_in_fee_setting.payment_method_location_rules = custom_rules
      end

      it 'does not override existing rules on validation' do
        dine_in_fee_setting.valid?
        expect(dine_in_fee_setting.payment_method_location_rules).to eq(custom_rules)
      end
    end
  end

  describe '#assign_charge_to_purchaser' do
    context 'when pg_fee and platform_fee charge_to_purchaser are already set' do
      before do
        dine_in_fee_setting.update!(
          va_charge_to_purchaser: true,
          va_pg_fee_charge_to_purchaser: false,
          va_platform_fee_charge_to_purchaser: false
        )
      end

      it 'does not override existing values' do
        dine_in_fee_setting.send(:assign_charge_to_purchaser)
        expect(dine_in_fee_setting.va_pg_fee_charge_to_purchaser).to be false
        expect(dine_in_fee_setting.va_platform_fee_charge_to_purchaser).to be false
      end
    end
  end

  describe '#set_zero_fee_for_foreign_country' do
    context 'when country is not in allowed list' do
      before { brand.update!(country: 'Singapore') }

      it 'disables all third party payment methods' do
        dine_in_fee_setting.send(:set_zero_fee_for_foreign_country)

        expect(dine_in_fee_setting.va_enable).to be false
        expect(dine_in_fee_setting.cc_enable).to be false
        expect(dine_in_fee_setting.gopay_enable).to be false
        expect(dine_in_fee_setting.ovo_enable).to be false
        expect(dine_in_fee_setting.dana_enable).to be false
        expect(dine_in_fee_setting.linkaja_enable).to be false
        expect(dine_in_fee_setting.shopeepay_enable).to be false
        expect(dine_in_fee_setting.sakuku_enable).to be false
        expect(dine_in_fee_setting.qris_enable).to be false
      end

      it 'sets platform fees to zero' do
        dine_in_fee_setting.send(:set_zero_fee_for_foreign_country)

        expect(dine_in_fee_setting.platform_fee_flat_rate).to eq(0)
        expect(dine_in_fee_setting.platform_fee_percentage_rate).to eq(0)
        expect(dine_in_fee_setting.via_cashier_platform_fee_flat_rate).to eq(0)
        expect(dine_in_fee_setting.full_balance_platform_fee_flat_rate).to eq(0)
      end
    end

    context 'when country is in allowed list' do
      before { brand.update!(country: 'Indonesia') }

      it 'does not modify payment settings' do
        original_va_enable = dine_in_fee_setting.va_enable
        original_platform_fee = dine_in_fee_setting.platform_fee_flat_rate

        dine_in_fee_setting.send(:set_zero_fee_for_foreign_country)

        expect(dine_in_fee_setting.va_enable).to eq(original_va_enable)
        expect(dine_in_fee_setting.platform_fee_flat_rate).to eq(original_platform_fee)
      end
    end
  end

  describe '#by_fee_selector' do
    context 'for regular payment methods (VA, CC, etc.)' do
      before do
        dine_in_fee_setting.update!(
          va_enable: true,
          va_pg_fee_flat_rate: 2000,
          va_pg_fee_percentage_rate: 1.5,
          va_platform_fee_flat_rate: 1000,
          va_platform_fee_percentage_rate: 0.5
        )
      end

      it 'returns combined PG and platform fees' do
        result = dine_in_fee_setting.by_fee_selector(:virtual_account)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(3000.0) # 2000 + 1000
        expect(result[:percentage_rate]).to eq(2.0) # 1.5 + 0.5
      end
    end

    context 'for via_cashier payment method' do
      before do
        dine_in_fee_setting.update!(
          via_cashier_enable: true,
          via_cashier_platform_fee_flat_rate: 1500,
          via_cashier_platform_fee_percentage_rate: 0.3
        )
      end

      it 'returns only platform fees' do
        result = dine_in_fee_setting.by_fee_selector(:cash)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(1500.0)
        expect(result[:percentage_rate]).to eq(0.3)
      end
    end

    context 'for full_balance payment method' do
      before do
        dine_in_fee_setting.update!(
          full_balance_enable: true,
          full_balance_platform_fee_flat_rate: 1200,
          full_balance_platform_fee_percentage_rate: 0.2
        )
      end

      it 'returns only platform fees' do
        result = dine_in_fee_setting.by_fee_selector(:balance)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(1200.0)
        expect(result[:percentage_rate]).to eq(0.2)
      end
    end

    context 'for unsupported payment method' do
      it 'raises InvalidParamsError' do
        expect {
          dine_in_fee_setting.by_fee_selector(:unsupported_method)
        }.to raise_error(Errors::InvalidParamsError, I18n.t('delivery.payments.errors.payment_method_not_supported'))
      end
    end
  end

  describe '#by_platform_fee_selector' do
    before do
      dine_in_fee_setting.update!(
        cc_enable: true,
        cc_platform_fee_flat_rate: 800,
        cc_platform_fee_percentage_rate: 0.4
      )
    end

    it 'returns only platform fee information' do
      result = dine_in_fee_setting.by_platform_fee_selector(:credit_card)

      expect(result[:enable]).to be true
      expect(result[:flat_rate]).to eq(800.0)
      expect(result[:percentage_rate]).to eq(0.4)
    end

    it 'raises error for unsupported payment method' do
      expect {
        dine_in_fee_setting.by_platform_fee_selector(:invalid_method)
      }.to raise_error(Errors::InvalidParamsError)
    end
  end

  describe '#by_pg_fee_selector' do
    context 'for regular payment methods' do
      before do
        dine_in_fee_setting.update!(
          gopay_enable: true,
          gopay_pg_fee_flat_rate: 1500,
          gopay_pg_fee_percentage_rate: 1.2
        )
      end

      it 'returns only PG fee information' do
        result = dine_in_fee_setting.by_pg_fee_selector(:gopay)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(1500.0)
        expect(result[:percentage_rate]).to eq(1.2)
      end
    end

    context 'for via_cashier and full_balance methods' do
      before do
        dine_in_fee_setting.update!(via_cashier_enable: true, full_balance_enable: true)
      end

      it 'returns zero PG fees for via_cashier' do
        result = dine_in_fee_setting.by_pg_fee_selector(:cash)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(0.0)
        expect(result[:percentage_rate]).to eq(0.0)
      end

      it 'returns zero PG fees for full_balance' do
        result = dine_in_fee_setting.by_pg_fee_selector(:balance)

        expect(result[:enable]).to be true
        expect(result[:flat_rate]).to eq(0.0)
        expect(result[:percentage_rate]).to eq(0.0)
      end
    end
  end

  describe 'payment method location rules validation' do
    describe '#validate_payment_method_location_rules' do
      context 'when payment_method_location_rules is blank' do
        before { dine_in_fee_setting.payment_method_location_rules = nil }

        it 'does not add validation errors' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_empty
        end
      end

      context 'when missing payment method keys' do
        before do
          dine_in_fee_setting.payment_method_location_rules = {
            'va' => { 'is_select_all_location' => true, 'location_ids' => [], 'exclude_location_ids' => [] }
            # Missing other payment methods
          }
        end

        it 'adds validation error for missing keys' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.missing_payment_method_keys', missing_keys: 'cc, gopay, ovo, dana, linkaja, shopeepay, sakuku, qris, via_cashier, full_balance')
          )
        end
      end

      context 'when payment method rule is not a hash' do
        before do
          # First trigger validation to set default rules
          dine_in_fee_setting.valid?
          rules = dine_in_fee_setting.payment_method_location_rules.dup
          rules['va'] = 'invalid_value'
          dine_in_fee_setting.payment_method_location_rules = rules
        end

        it 'adds validation error' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.payment_method_location_rule_must_be_hash', payment_method: 'va')
          )
        end
      end

      context 'when location rule fields are missing' do
        before do
          # First trigger validation to set default rules
          dine_in_fee_setting.valid?
          rules = dine_in_fee_setting.payment_method_location_rules.dup
          rules['va'] = { 'is_select_all_location' => true } # Missing location_ids and exclude_location_ids
          dine_in_fee_setting.payment_method_location_rules = rules
        end

        it 'adds validation error for missing fields' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.missing_location_rule_fields', payment_method: 'va', missing_keys: 'exclude_location_ids, location_ids')
          )
        end
      end

      context 'when is_select_all_location is not boolean' do
        before do
          # First trigger validation to set default rules
          dine_in_fee_setting.valid?
          rules = dine_in_fee_setting.payment_method_location_rules.dup
          rules['va']['is_select_all_location'] = 'invalid'
          dine_in_fee_setting.payment_method_location_rules = rules
        end

        it 'adds validation error' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.is_select_all_location_must_be_boolean', payment_method: 'va')
          )
        end
      end

      context 'when location_ids is not an array' do
        before do
          # First trigger validation to set default rules
          dine_in_fee_setting.valid?
          rules = dine_in_fee_setting.payment_method_location_rules.dup
          rules['va']['location_ids'] = 'invalid'
          dine_in_fee_setting.payment_method_location_rules = rules
        end

        it 'adds validation error' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.location_ids_must_be_array', payment_method: 'va')
          )
        end
      end

      context 'when exclude_location_ids is not an array' do
        before do
          # First trigger validation to set default rules
          dine_in_fee_setting.valid?
          rules = dine_in_fee_setting.payment_method_location_rules.dup
          rules['va']['exclude_location_ids'] = 'invalid'
          dine_in_fee_setting.payment_method_location_rules = rules
        end

        it 'adds validation error' do
          dine_in_fee_setting.valid?
          expect(dine_in_fee_setting.errors[:payment_method_location_rules]).to be_present
          expect(dine_in_fee_setting.errors[:payment_method_location_rules].first).to eq(
            I18n.t('dine_in_fee_setting.errors.exclude_location_ids_must_be_array', payment_method: 'va')
          )
        end
      end

      context 'with valid payment method location rules' do
        before do
          # Trigger validation to set default rules, then use them
          dine_in_fee_setting.valid?
          # The default rules are already set by the before_validation callback
        end

        it 'passes validation' do
          expect(dine_in_fee_setting).to be_valid
        end
      end
    end
  end

  describe 'separate charge_to_purchaser functionality' do
    context 'when updating charge_to_purchaser for different fee types' do
      it 'allows separate control of PG and platform fees' do
        dine_in_fee_setting.update!(
          va_charge_to_purchaser: true,
          va_pg_fee_charge_to_purchaser: false,
          va_platform_fee_charge_to_purchaser: true
        )

        expect(dine_in_fee_setting.va_charge_to_purchaser).to be true
        expect(dine_in_fee_setting.va_pg_fee_charge_to_purchaser).to be false
        expect(dine_in_fee_setting.va_platform_fee_charge_to_purchaser).to be true
      end

      it 'handles all payment methods separately' do
        payment_methods = %w[va cc gopay ovo dana linkaja shopeepay sakuku qris via_cashier full_balance]

        payment_methods.each do |method|
          dine_in_fee_setting.update!(
            "#{method}_charge_to_purchaser" => true,
            "#{method}_pg_fee_charge_to_purchaser" => false,
            "#{method}_platform_fee_charge_to_purchaser" => true
          )

          expect(dine_in_fee_setting.public_send("#{method}_charge_to_purchaser")).to be true
          expect(dine_in_fee_setting.public_send("#{method}_pg_fee_charge_to_purchaser")).to be false
          expect(dine_in_fee_setting.public_send("#{method}_platform_fee_charge_to_purchaser")).to be true
        end
      end
    end
  end

  describe 'auditing' do
    it 'is audited' do
      expect(dine_in_fee_setting.class.audited_options).to be_present
    end

    it 'creates audit records on update' do
      expect {
        dine_in_fee_setting.update!(va_enable: false)
      }.to change { dine_in_fee_setting.audits.count }.by(1)
    end
  end
end
