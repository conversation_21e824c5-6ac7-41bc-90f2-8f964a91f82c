class AddPaymentMethodLocationRulesToDineInFeeSettings < ActiveRecord::Migration[6.1]
  def change
    add_column :dine_in_fee_settings,
               :payment_method_location_rules,
               :jsonb,
               default: default_payment_method_location_rules,
               null: false
  end

  private

  def default_payment_method_location_rules
    {
      va: default_rule,
      cc: default_rule,
      gopay: default_rule,
      ovo: default_rule,
      dana: default_rule,
      linkaja: default_rule,
      shopeepay: default_rule,
      sakuku: default_rule,
      qris: default_rule,
      via_cashier: default_rule,
      full_balance: default_rule
    }
  end

  def default_rule
    {
      is_select_all_location: true,
      location_ids: [],
      exclude_location_ids: []
    }
  end
end
